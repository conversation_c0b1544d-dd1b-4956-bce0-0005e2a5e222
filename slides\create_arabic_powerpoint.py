#!/usr/bin/env python3
"""
Arabic PowerPoint Generator for Solar AI Cleaning & Monitoring System
Creates a professional Arabic presentation with enhanced cleaning methods and ROI analysis
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, <PERSON>O_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from pptx.chart.data import CategoryChartData
from pptx.enum.chart import XL_CHART_TYPE
import arabic_reshaper
from bidi.algorithm import get_display

def format_arabic_text(text):
    """Format Arabic text for proper display"""
    try:
        reshaped_text = arabic_reshaper.reshape(text)
        return get_display(reshaped_text)
    except:
        return text

# Create presentation
prs = Presentation()

# Define color scheme
BLUE_DARK = RGBColor(44, 62, 80)      # #2c3e50
BLUE_LIGHT = RGBColor(52, 152, 219)   # #3498db
GREEN = RGBColor(39, 174, 96)         # #27ae60
ORANGE = RGBColor(243, 156, 18)       # #f39c12
RED = RGBColor(231, 76, 60)           # #e74c3c
PURPLE = RGBColor(155, 89, 182)       # #9b59b6
WHITE = RGBColor(255, 255, 255)
GRAY_LIGHT = RGBColor(236, 240, 241)  # #ecf0f1

def add_title_slide():
    """Add enhanced title slide"""
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    # Main title
    title.text = format_arabic_text("نظام مراقبة وتنظيف الألواح الشمسية المدعوم بالذكاء الاصطناعي")
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    # Subtitle
    subtitle.text = format_arabic_text("9 طرق تنظيف متقدمة • ذكاء اصطناعي متطور • توفير 50% في التكاليف")
    subtitle.text_frame.paragraphs[0].font.size = Pt(20)
    subtitle.text_frame.paragraphs[0].font.color.rgb = BLUE_LIGHT
    
    return slide

def add_problem_slide():
    """Add problem statement slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("التحديات الحالية في صيانة الألواح الشمسية")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    problems = [
        "تراكم الغبار يقلل من كفاءة الألواح بنسبة تصل إلى 35%",
        "الطرق التقليدية تستهلك كميات كبيرة من المياه (0.7 لتر/م²)",
        "التكاليف التشغيلية العالية للصيانة اليدوية",
        "صعوبة اكتشاف العيوب والشقوق في الوقت المناسب",
        "عدم وجود جدولة ذكية للصيانة الوقائية",
        "محدودية خيارات التنظيف حسب الظروف البيئية"
    ]
    
    for i, problem in enumerate(problems):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(f"• {problem}")
        p.font.size = Pt(16)
        p.font.color.rgb = BLUE_DARK
        p.space_after = Pt(8)
    
    return slide

def add_enhanced_methods_slide():
    """Add enhanced cleaning methods slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("9 طرق تنظيف متقدمة ومبتكرة")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    methods = [
        "🚁 تنظيف بالطائرات المسيرة (بالمياه وبدون مياه)",
        "🤖 روبوتات الزحف الذاتية - كفاءة 95%",
        "✨ الطلاءات النانوية ذاتية التنظيف",
        "💨 أنظمة النفخ بالهواء المضغوط",
        "🌊 الاهتزازات فوق الصوتية",
        "⚡ التنظيف الكهروستاتيكي (جديد)",
        "☀️ التنظيف بالأشعة فوق البنفسجية (جديد)",
        "🧠 الصيانة التنبؤية بالذكاء الاصطناعي (جديد)",
        "📊 نظام اختيار الطريقة الأمثل تلقائياً"
    ]
    
    for i, method in enumerate(methods):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(method)
        p.font.size = Pt(16)
        p.font.color.rgb = GREEN if "جديد" in method else BLUE_DARK
        p.font.bold = "جديد" in method
        p.space_after = Pt(6)
    
    return slide

def add_iot_sensors_slide():
    """Add IoT sensors integration slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("تكامل أجهزة الاستشعار الذكية (IoT)")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    sensors = [
        "🌡️ مستشعرات الحرارة: مراقبة درجة حرارة الألواح والكفاءة",
        "💧 مستشعرات الرطوبة: توقع التكثف واحتياجات التنظيف",
        "⚡ مراقبة الجهد والتيار: أداء كهربائي في الوقت الفعلي",
        "📳 مستشعرات الاهتزاز: كشف المشاكل الميكانيكية",
        "🌪️ كثافة الغبار: مراقبة مستوى الغبار البيئي",
        "🌤️ تكامل الطقس: بيانات CAMS الجوية",
        "📱 تطبيق الهاتف المحمول: مراقبة ومتابعة شاملة",
        "🔔 تنبيهات ذكية: إشعارات متعددة القنوات"
    ]
    
    for i, sensor in enumerate(sensors):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(sensor)
        p.font.size = Pt(16)
        p.font.color.rgb = PURPLE
        p.space_after = Pt(6)
    
    return slide

def add_roi_analysis_slide():
    """Add enhanced ROI analysis slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("تحليل العائد على الاستثمار المحسن")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    roi_data = [
        "💰 صافي الفائدة خلال 5 سنوات: 8.2 مليون دولار (100 ميجاوات)",
        "📈 العائد على الاستثمار: 410% خلال 5 سنوات",
        "⏱️ فترة الاسترداد: 18 شهراً فقط",
        "💵 التوفير السنوي: 1.6 مليون دولار",
        "🌊 توفير المياه: 85% (2.5 مليون لتر سنوياً)",
        "⚡ زيادة الكفاءة: 25% في إنتاج الطاقة",
        "🔧 تقليل تكاليف الصيانة: 50%",
        "🌱 تقليل انبعاثات الكربون: 7,500 طن سنوياً"
    ]
    
    for i, data in enumerate(roi_data):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(data)
        p.font.size = Pt(16)
        p.font.color.rgb = GREEN
        p.font.bold = True
        p.space_after = Pt(6)
    
    return slide

def add_predictive_scheduling_slide():
    """Add predictive scheduling slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("الجدولة التنبؤية بالذكاء الاصطناعي")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    scheduling_features = [
        "🔮 التنبؤ بالطقس: توقعات الغبار من CAMS",
        "📊 مراقبة الكفاءة: بيانات الأداء في الوقت الفعلي",
        "📅 الأنماط الموسمية: تراكم الغبار التاريخي",
        "💡 تحسين التكلفة: اختيار الطريقة بناءً على العائد",
        "🛠️ توفر الموارد: جدولة المعدات والفرق",
        "🎯 الجدولة الذكية: تنظيف استباقي قبل انخفاض الكفاءة",
        "📱 واجهة سهلة: تطبيق محمول للمراقبة والتحكم",
        "🔄 التحديث التلقائي: تعديل الجدولة حسب الظروف"
    ]
    
    for i, feature in enumerate(scheduling_features):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(feature)
        p.font.size = Pt(16)
        p.font.color.rgb = ORANGE
        p.space_after = Pt(6)
    
    return slide

def add_competitive_advantages_slide():
    """Add competitive advantages slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("المزايا التنافسية الفريدة")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    advantages = [
        "🥇 الأول عالمياً: 9 طرق تنظيف متكاملة",
        "🤖 ذكاء اصطناعي متقدم: YOLOv9 + التصوير الحراري",
        "📱 تطبيق محمول احترافي: iOS و Android",
        "🌐 دعم اللغة العربية: واجهات محلية متكاملة",
        "💧 صديق للبيئة: توفير 85% من المياه",
        "⚡ كفاءة عالية: 99% دقة في الكشف",
        "💰 توفير مضمون: 50% تقليل في التكاليف",
        "🔮 صيانة تنبؤية: منع الأعطال قبل حدوثها"
    ]
    
    for i, advantage in enumerate(advantages):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(advantage)
        p.font.size = Pt(16)
        p.font.color.rgb = BLUE_DARK
        p.font.bold = True
        p.space_after = Pt(6)
    
    return slide

def add_technology_stack_slide():
    """Add technology stack slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("المكونات التقنية المتكاملة")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    tech_components = [
        "🧠 الذكاء الاصطناعي: YOLOv9 + OpenCV + TensorFlow",
        "🌐 الواجهة الويب: React + FastAPI + WebSocket",
        "📱 التطبيق المحمول: React Native + Redux",
        "🔗 إنترنت الأشياء: Arduino + Raspberry Pi + MQTT",
        "☁️ الحوسبة السحابية: Docker + Kubernetes",
        "📊 قاعدة البيانات: PostgreSQL + Redis",
        "🔒 الأمان: JWT + تشفير البيانات",
        "📈 التحليلات: Grafana + Prometheus"
    ]
    
    for i, component in enumerate(tech_components):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(component)
        p.font.size = Pt(16)
        p.font.color.rgb = PURPLE
        p.space_after = Pt(6)
    
    return slide

def add_environmental_impact_slide():
    """Add environmental impact slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("الأثر البيئي والاستدامة")
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    environmental_benefits = [
        "💧 توفير المياه: 2.5 مليون لتر سنوياً (مزرعة 500 ميجاوات)",
        "🌱 تقليل الكربون: 7,500 طن CO₂ سنوياً",
        "⚡ طاقة إضافية: 75,000 ميجاوات ساعة سنوياً",
        "🌍 دعم أهداف التنمية المستدامة للأمم المتحدة",
        "♻️ تقنيات صديقة للبيئة: تنظيف بدون مياه",
        "🌿 حماية النظام البيئي الصحراوي",
        "📈 زيادة إنتاج الطاقة النظيفة بنسبة 25%",
        "🎯 مساهمة في رؤية السعودية 2030"
    ]
    
    for i, benefit in enumerate(environmental_benefits):
        p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
        p.text = format_arabic_text(benefit)
        p.font.size = Pt(16)
        p.font.color.rgb = GREEN
        p.space_after = Pt(6)
    
    return slide

def add_cta_slide():
    """Add call to action slide"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = format_arabic_text("انضم إلى ثورة الطاقة الشمسية الذكية")
    title.text_frame.paragraphs[0].font.size = Pt(28)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = BLUE_DARK
    
    content = slide.placeholders[1]
    tf = content.text_frame
    tf.clear()
    
    cta_content = [
        "🚀 ابدأ رحلتك نحو المستقبل المستدام",
        "📞 احجز استشارة مجانية اليوم",
        "💡 اكتشف كيف يمكن للذكاء الاصطناعي تحويل عملياتك",
        "📊 احصل على تحليل مخصص لمشروعك",
        "",
        "📧 <EMAIL>",
        "📱 +966 50 123 4567",
        "🌐 www.solar-ai-monitoring.com",
        "",
        "🌞 معاً نبني مستقبل الطاقة النظيفة 🤖"
    ]
    
    for i, content_item in enumerate(cta_content):
        if content_item:  # Skip empty lines
            p = tf.paragraphs[i] if i == 0 else tf.add_paragraph()
            p.text = format_arabic_text(content_item)
            p.font.size = Pt(18) if i < 4 else Pt(16)
            p.font.color.rgb = BLUE_DARK if i < 4 else ORANGE
            p.font.bold = i < 4
            p.space_after = Pt(8)
        else:
            tf.add_paragraph()
    
    return slide

def main():
    """Generate the complete Arabic presentation"""
    print("إنشاء العرض التقديمي العربي لنظام مراقبة الألواح الشمسية...")
    
    # Add all slides
    add_title_slide()
    add_problem_slide()
    add_enhanced_methods_slide()
    add_iot_sensors_slide()
    add_roi_analysis_slide()
    add_predictive_scheduling_slide()
    add_competitive_advantages_slide()
    add_technology_stack_slide()
    add_environmental_impact_slide()
    add_cta_slide()
    
    # Save presentation
    filename = "نظام_مراقبة_الألواح_الشمسية_المحسن.pptx"
    prs.save(filename)
    print(f"تم حفظ العرض التقديمي: {filename}")
    print("✅ تم إنشاء العرض التقديمي العربي بنجاح!")
    print("🌟 يتضمن العرض 9 طرق تنظيف متقدمة وتحليل ROI محسن")

if __name__ == "__main__":
    main()
