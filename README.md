# Solar AI Cleaning & Monitoring System

A comprehensive AI-powered solution for monitoring and cleaning solar panels in desert environments, specifically designed for the Middle East region.

## 🌟 Features

- **AI-Powered Detection**: YOLOv8-based dust and crack detection
- **Thermal Monitoring**: FLIR integration for hotspot detection
- **Predictive Analytics**: Weather-integrated cleaning scheduling
- **Real-time Dashboard**: Bilingual (Arabic/English) monitoring interface
- **Automated Notifications**: WhatsApp alerts via Twilio
- **Battery Optimization**: SOC-based cleaning scheduling
- **Comprehensive Analytics**: ROI calculation and performance tracking

## 📋 Table of Contents

- [Installation](#installation)
- [Quick Start](#quick-start)
- [Local Development](#local-development)
- [Google Colab Usage](#google-colab-usage)
- [API Documentation](#api-documentation)
- [Dashboard Usage](#dashboard-usage)
- [Configuration](#configuration)
- [Hardware Setup](#hardware-setup)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

## 🚀 Installation

### Prerequisites

- Python 3.8 or higher
- Node.js 16 or higher (for dashboard)
- Git

### Clone Repository

```bash
git clone https://github.com/your-username/solar-ai-monitoring.git
cd solar-ai-monitoring
```

### Python Dependencies

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Dashboard Dependencies

```bash
cd solar-dashboard
npm install
```

## ⚡ Quick Start

### 1. Start the API Server

```bash
python fastapi_app.py
```

The API will be available at `http://localhost:8000`

### 2. Start the Dashboard

```bash
cd solar-dashboard
npm run dev
```

The dashboard will be available at `http://localhost:5173`

### 3. Run YOLO Training (Optional)

```bash
# Open the Jupyter notebook
jupyter notebook train_yolov8.ipynb
```

## 💻 Local Development

### Environment Setup

1. **Configure Environment Variables**

Create a `.env` file in the project root:

```env
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token

# Weather API
WEATHER_API_KEY=your_openweather_api_key

# CAMS API (for dust data)
CAMS_API_KEY=your_cams_api_key
```

2. **Database Setup** (if using database)

```bash
# Initialize database
python scripts/init_db.py
```

### Running Individual Components

#### Dust Forecast

```bash
python dust_forecast.py
```

#### Battery Scheduler

```bash
python battery_scheduler.py
```

#### Notification System

```bash
python notify.py --check-production
```

#### CAMS Data Processing

```bash
python cams_to_json.py
```

## 📊 Google Colab Usage

### 1. Upload to Google Drive

1. Upload the entire project folder to your Google Drive
2. Open Google Colab
3. Mount your Google Drive

```python
from google.colab import drive
drive.mount('/content/drive')
```

### 2. Navigate to Project

```python
import os
os.chdir('/content/drive/MyDrive/solar-ai-monitoring')
```

### 3. Install Dependencies

```python
!pip install ultralytics fastapi uvicorn twilio requests pandas numpy matplotlib seaborn
```

### 4. Run YOLO Training

```python
# Open and run the training notebook
%run train_yolov8.ipynb
```

### 5. Test API Endpoints

```python
# Start FastAPI server in background
import subprocess
import time

# Start server
process = subprocess.Popen(['python', 'fastapi_app.py'])
time.sleep(5)  # Wait for server to start

# Test API
import requests
response = requests.get('http://localhost:8000/api/panel-status')
print(response.json())
```

## 📡 API Documentation

### Base URL
- Local: `http://localhost:8000`
- Production: `https://your-domain.com`

### Endpoints

#### Get Panel Status
```http
GET /api/panel-status
```

Response:
```json
[
  {
    "panel_id": "A23",
    "location": {"lat": 24.7136, "lng": 46.6753},
    "status": "clean",
    "dust_level": 5.0,
    "efficiency": 95.0,
    "last_cleaned": "2025-06-18",
    "defects": [],
    "battery_level": 85.0,
    "temperature": 35.2
  }
]
```

#### Get Specific Panel
```http
GET /api/panel-status/{panel_id}
```

#### Update Panel Status
```http
POST /api/panel-status/{panel_id}/update
Content-Type: application/json

{
  "dust_level": 15.0,
  "efficiency": 85.0,
  "status": "dusty"
}
```

#### Get System Statistics
```http
GET /api/stats
```

#### Schedule Cleaning
```http
POST /api/cleaning/schedule
Content-Type: application/json

{
  "panel_ids": ["A23", "B15"]
}
```

## 🖥️ Dashboard Usage

### Language Switching

The dashboard supports both Arabic and English:
- Click the language toggle button in the top-right corner
- Arabic mode includes RTL (right-to-left) text direction
- All interface elements are fully translated

### Panel Monitoring

- **Status Cards**: Overview of total panels, active panels, cleaning scheduled, and battery level
- **Panel Grid**: Detailed view of each panel with:
  - Efficiency percentage and progress bar
  - Dust level with visual indicator
  - Last cleaned date
  - GPS coordinates
  - Detected defects (if any)

### Real-time Updates

The dashboard automatically updates panel data every 30 seconds when connected to the API.

## ⚙️ Configuration

### Notification Configuration

Create `notification_config.json`:

```json
{
  "twilio_account_sid": "your_sid",
  "twilio_auth_token": "your_token",
  "from_whatsapp_number": "whatsapp:+***********",
  "to_whatsapp_number": "whatsapp:+**********",
  "production_threshold": 80,
  "notification_cooldown_hours": 2,
  "language": "en",
  "recipients": [
    {"name": "Site Manager", "number": "whatsapp:+**********"},
    {"name": "Maintenance Team", "number": "whatsapp:+**********"}
  ]
}
```

### Scheduler Configuration

Create `scheduler_config.json`:

```json
{
  "min_soc_threshold": 70,
  "cleaning_power_consumption": 500,
  "cleaning_duration": 30,
  "preferred_cleaning_hours": [6, 7, 8, 16, 17, 18],
  "weather_api_key": "your_api_key",
  "panels": [
    {"id": "A23", "priority": 1, "last_cleaned": "2025-06-18"},
    {"id": "B15", "priority": 2, "last_cleaned": "2025-06-10"},
    {"id": "C07", "priority": 3, "last_cleaned": "2025-06-05"}
  ]
}
```

## 🔧 Hardware Setup

### Arduino MPU6050 Setup

1. **Wiring**:
   - MPU6050 VCC → 3.3V
   - MPU6050 GND → GND
   - MPU6050 SCL → A5 (Uno) or GPIO 22 (ESP32)
   - MPU6050 SDA → A4 (Uno) or GPIO 21 (ESP32)

2. **Libraries Required**:
   - MPU6050 library by Electronic Cats
   - PubSubClient for MQTT
   - WiFi library (ESP32) or WiFiEsp (Arduino Uno)
   - ArduinoJson for JSON formatting

3. **Configuration**:
   - Update WiFi credentials in `arduino_mpu6050_mqtt.ino`
   - Set MQTT broker details
   - Upload to Arduino/ESP32

### Camera Setup

For YOLO training and inference:
- Sony IMX477 camera for RGB imaging
- FLIR thermal camera for hotspot detection
- Ensure proper mounting and calibration

## 🚀 Deployment

### Local Deployment

1. **Start all services**:
```bash
# Start API server
python fastapi_app.py &

# Start dashboard
cd solar-dashboard && npm run build && npm run preview &

# Start monitoring services
./daily_monitoring.sh
```

2. **Set up cron job**:
```bash
# Add to crontab
crontab -e

# Add this line for daily monitoring at 6 AM
0 6 * * * /path/to/solar-ai-monitoring/daily_monitoring.sh
```

### Cloud Deployment

#### Using Docker

```dockerfile
# Dockerfile example
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "fastapi_app.py"]
```

#### Using GitHub Actions

The project includes a GitHub Actions workflow (`.github/workflows/daily-monitoring.yml`) that:
- Runs daily at 6 AM UTC
- Downloads latest CAMS dust data
- Processes forecasts
- Checks panel production
- Updates cleaning schedules
- Commits results to repository

### Environment Variables for Production

```env
# Production settings
ENVIRONMENT=production
API_HOST=0.0.0.0
API_PORT=8000

# Database (if using)
DATABASE_URL=postgresql://user:pass@localhost/solar_monitoring

# External APIs
TWILIO_ACCOUNT_SID=your_production_sid
TWILIO_AUTH_TOKEN=your_production_token
WEATHER_API_KEY=your_production_key
CAMS_API_KEY=your_production_cams_key

# Security
SECRET_KEY=your_secret_key
ALLOWED_HOSTS=your-domain.com,api.your-domain.com
```

## 📊 ROI Calculator

The Excel-based ROI calculator (`roi_calculator.xlsx`) helps estimate:
- Annual energy loss due to dust
- Cleaning costs vs. energy savings
- Return on investment timeline
- Comparative analysis with manual cleaning

### Usage

1. Open `roi_calculator.xlsx`
2. Input your specific parameters:
   - Daily energy production (kWh)
   - Dust accumulation rate (%)
   - Electricity cost ($/kWh)
   - Cleaning frequency and cost
3. Review calculated savings and ROI

## 🧪 Testing

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run all tests
pytest

# Run with coverage
pytest --cov=.
```

### API Testing

```bash
# Test API endpoints
python -m pytest tests/test_api.py

# Manual testing with curl
curl -X GET http://localhost:8000/api/panel-status
curl -X POST http://localhost:8000/api/panel-status/A23/update \
  -H "Content-Type: application/json" \
  -d '{"dust_level": 20.0}'
```

### Dashboard Testing

```bash
cd solar-dashboard
npm test
```

## 🔍 Troubleshooting

### Common Issues

1. **API Server Won't Start**
   - Check if port 8000 is already in use
   - Verify Python dependencies are installed
   - Check environment variables

2. **Dashboard Not Loading**
   - Ensure Node.js dependencies are installed
   - Check if development server is running
   - Verify API connection

3. **YOLO Training Issues**
   - Ensure sufficient GPU memory
   - Check dataset format and paths
   - Verify CUDA installation (for GPU training)

4. **Notification Failures**
   - Verify Twilio credentials
   - Check WhatsApp sandbox setup
   - Ensure phone numbers are properly formatted

5. **Arduino Connection Issues**
   - Check wiring connections
   - Verify WiFi credentials
   - Ensure MQTT broker is accessible

### Logs and Debugging

- API logs: Check console output when running `fastapi_app.py`
- Dashboard logs: Check browser developer console
- Monitoring logs: Check `logs/daily_monitoring_*.log`
- Arduino logs: Check serial monitor in Arduino IDE

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow PEP 8 for Python code
- Use ESLint for JavaScript/React code
- Add tests for new features
- Update documentation as needed
- Ensure bilingual support for UI changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- CAMS (Copernicus Atmosphere Monitoring Service) for dust data
- Ultralytics for YOLOv8 implementation
- Twilio for communication services
- OpenWeatherMap for weather data
- The open-source community for various libraries and tools

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [https://docs.solar-ai-monitoring.com](https://docs.solar-ai-monitoring.com)
- Issues: [GitHub Issues](https://github.com/your-username/solar-ai-monitoring/issues)

---

**Made with ❤️ for sustainable energy in the Middle East**

