<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar AI Cleaning & Monitoring System - Revolutionary Solar Panel Maintenance</title>
    <meta name="description" content="Advanced AI-powered solar panel cleaning and monitoring system with 99% detection accuracy, 50% cost reduction, and 85% water savings.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <style>
        :root {
            --primary-blue: #1e3a8a;
            --secondary-green: #059669;
            --accent-orange: #ea580c;
            --gold: #f59e0b;
        }
        
        .gradient-primary {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        }
        
        .gradient-secondary {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        }
        
        .gradient-accent {
            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
        }
        
        .hero-bg {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .stat-counter {
            font-family: 'Poppins', sans-serif;
            font-weight: 800;
        }
        
        .feature-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .animate-pulse-slow {
            animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .method-badge {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 8px;
        }
    </style>
</head>

<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-xl">☀️</span>
                        </div>
                        <span class="ml-3 text-xl font-bold text-gray-900">Solar AI</span>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-gray-700 hover:text-blue-600 font-medium">Features</a>
                    <a href="#technology" class="text-gray-700 hover:text-blue-600 font-medium">Technology</a>
                    <a href="#roi" class="text-gray-700 hover:text-blue-600 font-medium">ROI</a>
                    <a href="#contact" class="text-gray-700 hover:text-blue-600 font-medium">Contact</a>
                    <button class="gradient-primary text-white px-6 py-2 rounded-lg font-semibold hover:opacity-90 transition-opacity">
                        Get Demo
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-bg text-white pt-20 pb-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-900/50 to-transparent"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h1 class="text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                        Revolutionary
                        <span class="bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
                            AI-Powered
                        </span>
                        Solar Panel Maintenance
                    </h1>
                    <p class="text-xl text-gray-300 mb-8 leading-relaxed">
                        Transform your solar operations with 99% AI detection accuracy, 50% cost reduction, 
                        and 85% water savings. The future of solar panel maintenance is here.
                    </p>
                    
                    <!-- Key Stats -->
                    <div class="grid grid-cols-3 gap-6 mb-8">
                        <div class="text-center">
                            <div class="stat-counter text-3xl font-bold text-blue-400">99%</div>
                            <div class="text-sm text-gray-400">AI Accuracy</div>
                        </div>
                        <div class="text-center">
                            <div class="stat-counter text-3xl font-bold text-green-400">50%</div>
                            <div class="text-sm text-gray-400">Cost Reduction</div>
                        </div>
                        <div class="text-center">
                            <div class="stat-counter text-3xl font-bold text-orange-400">85%</div>
                            <div class="text-sm text-gray-400">Water Savings</div>
                        </div>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button class="gradient-primary px-8 py-4 rounded-lg font-semibold text-lg hover:opacity-90 transition-opacity">
                            Start Free Trial
                        </button>
                        <button class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-gray-900 transition-colors">
                            Watch Demo
                        </button>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="animate-float">
                        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
                            <h3 class="text-2xl font-bold mb-6">Live System Status</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span>Panels Monitored</span>
                                    <span class="font-bold text-green-400">1,247</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Efficiency Gain</span>
                                    <span class="font-bold text-blue-400">+25%</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Water Saved Today</span>
                                    <span class="font-bold text-orange-400">2,840L</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Cost Savings</span>
                                    <span class="font-bold text-green-400">$12,450</span>
                                </div>
                            </div>
                            <div class="mt-6 h-2 bg-gray-700 rounded-full overflow-hidden">
                                <div class="h-full gradient-secondary rounded-full animate-pulse-slow" style="width: 87%"></div>
                            </div>
                            <p class="text-sm text-gray-400 mt-2">System Performance: 87%</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">9 Advanced Cleaning Methods</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    From traditional water-based cleaning to cutting-edge electrostatic and UV technologies
                </p>
            </div>
            
            <div class="tech-grid">
                <!-- Method 1 -->
                <div class="bg-white rounded-xl shadow-lg p-6 card-hover border border-gray-100">
                    <div class="text-4xl mb-4">🚁</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Drone Water Cleaning</h3>
                    <p class="text-gray-600 mb-4">Traditional water-based drone cleaning with precision targeting</p>
                    <div class="flex justify-between text-sm">
                        <span class="text-blue-600 font-semibold">Efficiency: 85%</span>
                        <span class="text-gray-500">Water: 0.7 L/m²</span>
                    </div>
                </div>
                
                <!-- Method 2 -->
                <div class="bg-white rounded-xl shadow-lg p-6 card-hover border border-gray-100">
                    <div class="text-4xl mb-4">⚡</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Drone Waterless</h3>
                    <p class="text-gray-600 mb-4">Advanced waterless drone technology for water-scarce regions</p>
                    <div class="flex justify-between text-sm">
                        <span class="text-green-600 font-semibold">Efficiency: 90%</span>
                        <span class="text-green-500">Water: 0.0 L/m²</span>
                    </div>
                </div>
                
                <!-- Method 3 -->
                <div class="bg-white rounded-xl shadow-lg p-6 card-hover border border-gray-100">
                    <div class="text-4xl mb-4">🤖</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Crawler Robots</h3>
                    <p class="text-gray-600 mb-4">Autonomous robots that crawl directly on panel surfaces</p>
                    <div class="flex justify-between text-sm">
                        <span class="text-green-600 font-semibold">Efficiency: 95%</span>
                        <span class="text-green-500">Water: 0.1 L/m²</span>
                    </div>
                </div>
                
                <!-- Method 4 -->
                <div class="bg-white rounded-xl shadow-lg p-6 card-hover border border-gray-100">
                    <div class="text-4xl mb-4">⚡</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">
                        Electrostatic Cleaning
                        <span class="method-badge">NEW</span>
                    </h3>
                    <p class="text-gray-600 mb-4">Revolutionary electrostatic charge technology for dust repulsion</p>
                    <div class="flex justify-between text-sm">
                        <span class="text-blue-600 font-semibold">Efficiency: 85%</span>
                        <span class="text-green-500">Water: 0.0 L/m²</span>
                    </div>
                </div>
                
                <!-- Method 5 -->
                <div class="bg-white rounded-xl shadow-lg p-6 card-hover border border-gray-100">
                    <div class="text-4xl mb-4">☀️</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">
                        UV Surface Cleaning
                        <span class="method-badge">NEW</span>
                    </h3>
                    <p class="text-gray-600 mb-4">UV light treatment for organic contamination removal</p>
                    <div class="flex justify-between text-sm">
                        <span class="text-blue-600 font-semibold">Efficiency: 65%</span>
                        <span class="text-green-500">Water: 0.0 L/m²</span>
                    </div>
                </div>
                
                <!-- Method 6 -->
                <div class="bg-white rounded-xl shadow-lg p-6 card-hover border border-gray-100">
                    <div class="text-4xl mb-4">🧠</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">
                        Predictive Maintenance
                        <span class="method-badge">NEW</span>
                    </h3>
                    <p class="text-gray-600 mb-4">AI-driven scheduling optimizes all methods based on conditions</p>
                    <div class="flex justify-between text-sm">
                        <span class="text-purple-600 font-semibold">Smart Optimization</span>
                        <span class="text-gray-500">All Methods</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">Ready to Transform Your Solar Operations?</h2>
                <p class="text-xl text-gray-300">Get started with a free consultation and see the difference AI can make</p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12">
                <div>
                    <h3 class="text-2xl font-bold mb-6">Contact Information</h3>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <span class="text-blue-400">📧</span>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-blue-400">📱</span>
                            <span>+966 50 123 4567</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-blue-400">🌐</span>
                            <span>www.solar-ai-monitoring.com</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-blue-400">📍</span>
                            <span>Riyadh, Saudi Arabia</span>
                        </div>
                    </div>

                    <div class="mt-8 p-6 bg-gradient-to-r from-blue-600 to-green-600 rounded-xl">
                        <h4 class="text-xl font-bold mb-4">Key Benefits</h4>
                        <ul class="space-y-2">
                            <li class="flex items-center space-x-2">
                                <span class="text-green-300">✓</span>
                                <span>99% AI Detection Accuracy</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <span class="text-green-300">✓</span>
                                <span>50% Cost Reduction</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <span class="text-green-300">✓</span>
                                <span>85% Water Savings</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <span class="text-green-300">✓</span>
                                <span>18-Month ROI Payback</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-xl p-8">
                    <h3 class="text-2xl font-bold mb-6">Get Free Demo</h3>
                    <form class="space-y-4">
                        <input type="text" placeholder="Your Name" class="w-full p-3 rounded-lg bg-gray-700 text-white placeholder-gray-400 border border-gray-600 focus:border-blue-500 focus:outline-none">
                        <input type="email" placeholder="Email Address" class="w-full p-3 rounded-lg bg-gray-700 text-white placeholder-gray-400 border border-gray-600 focus:border-blue-500 focus:outline-none">
                        <input type="text" placeholder="Company Name" class="w-full p-3 rounded-lg bg-gray-700 text-white placeholder-gray-400 border border-gray-600 focus:border-blue-500 focus:outline-none">
                        <select class="w-full p-3 rounded-lg bg-gray-700 text-white border border-gray-600 focus:border-blue-500 focus:outline-none">
                            <option>Installation Size</option>
                            <option>1-10 MW</option>
                            <option>10-50 MW</option>
                            <option>50-100 MW</option>
                            <option>100+ MW</option>
                        </select>
                        <textarea placeholder="Tell us about your solar installation and challenges" rows="4" class="w-full p-3 rounded-lg bg-gray-700 text-white placeholder-gray-400 border border-gray-600 focus:border-blue-500 focus:outline-none"></textarea>
                        <button type="submit" class="w-full gradient-primary py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity">
                            Schedule Free Demo
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-xl">☀️</span>
                    </div>
                    <span class="ml-3 text-2xl font-bold">Solar AI</span>
                </div>
                <p class="text-gray-400 mb-4">Revolutionizing Solar Panel Maintenance with AI-Powered Technology</p>
                <div class="flex justify-center space-x-8 mb-6">
                    <span class="text-blue-400 font-semibold">99% AI Accuracy</span>
                    <span class="text-green-400 font-semibold">50% Cost Reduction</span>
                    <span class="text-orange-400 font-semibold">85% Water Savings</span>
                </div>
                <p class="text-gray-500 text-sm">© 2025 Solar AI Cleaning & Monitoring System. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-counter');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent);
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target + (counter.textContent.includes('%') ? '%' : '');
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current) + (counter.textContent.includes('%') ? '%' : '');
                    }
                }, 20);
            });
        }

        // Trigger counter animation when page loads
        window.addEventListener('load', animateCounters);

        // Add scroll effect to navigation
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.classList.add('bg-white/95', 'backdrop-blur-lg');
            } else {
                nav.classList.remove('bg-white/95', 'backdrop-blur-lg');
            }
        });
    </script>
</body>
</html>
